# Bulk Email Payload Size Fix

## Problem
The bulk email functionality was failing with "payload too large" errors when sending emails to many guests (hundreds or thousands). This happened because:

1. **Large JSON Payload**: The frontend was sending all guest data (complete objects) in a single HTTP request
2. **Memory Issues**: Processing hundreds/thousands of guest objects at once consumed too much memory
3. **Request Size Limits**: HTTP request body exceeded default size limits

## Solution Implemented

### 1. Batch Processing Architecture
- **Frontend**: Now sends only guest IDs instead of full guest objects
- **Backend**: Fetches guest data from database in configurable batches
- **Progress Tracking**: Real-time progress updates during batch processing

### 2. New API Endpoints

#### `/private/api/send-all-emails` (Updated)
- Now accepts `guestIds` array instead of `guests` array
- Processes guests in batches (default: 50 per batch)
- Fetches guest data from database for each batch
- Handles batch failures gracefully

#### `/private/api/send-emails-batch` (New)
- Alternative endpoint for smaller batch processing
- Processes single batch at a time
- Better for very large datasets
- Includes detailed progress tracking

### 3. Frontend Improvements

#### Batch Email Utility (`src/lib/utils/batchEmailSender.ts`)
- Utility function for client-side batch processing
- Progress tracking with callbacks
- Error handling for individual batches
- Configurable batch sizes

#### UI Enhancements
- Real-time progress indicator during bulk email sending
- Shows current batch progress and overall statistics
- Visual progress bar with success/failure counts

### 4. Configuration Options

#### Batch Sizes
- **Main API**: 50 guests per batch (configurable)
- **Batch API**: 25 guests per batch (configurable)
- **Frontend**: Adjustable via `batchSize` parameter

#### Memory Management
- Processes guests in small chunks to avoid memory issues
- Adds delays between batches to prevent server overload
- Graceful error handling for individual batch failures

## Usage

### For Large Datasets (Recommended)
The system now automatically uses batch processing when sending emails to all guests:

```javascript
// Frontend automatically sends only IDs
const guestIds = filteredGuests.map(guest => guest.id.toString());

// Backend fetches data in batches
const result = await sendEmailsInBatches(guestIds, eventDetails, 25);
```

### For Custom Batch Processing
You can use the batch utility directly:

```javascript
import { sendEmailsInBatches } from '$lib/utils/batchEmailSender';

const result = await sendEmailsInBatches(
    guestIds,
    eventDetails,
    25, // batch size
    (progress) => {
        console.log(`Progress: ${progress.currentBatch}/${progress.totalBatches}`);
    }
);
```

## Benefits

1. **Scalability**: Can handle thousands of guests without payload size issues
2. **Reliability**: Batch failures don't stop the entire process
3. **Performance**: Better memory management and server resource usage
4. **User Experience**: Real-time progress tracking and feedback
5. **Maintainability**: Modular architecture with reusable utilities

## Testing

To test the fix:

1. **Small Dataset**: Test with 10-50 guests to verify normal functionality
2. **Medium Dataset**: Test with 100-500 guests to verify batch processing
3. **Large Dataset**: Test with 1000+ guests to verify scalability
4. **Error Handling**: Test with invalid guest data to verify error handling
5. **Progress Tracking**: Verify progress indicator updates correctly

## Configuration

### Batch Size Tuning
- **Small batches (10-25)**: Better for slower servers or rate-limited APIs
- **Medium batches (25-50)**: Good balance for most use cases
- **Large batches (50-100)**: For powerful servers with good network

### Memory Considerations
- Monitor server memory usage during bulk operations
- Adjust batch sizes based on available server resources
- Consider adding more delays between batches if needed

## Monitoring

The system now includes comprehensive logging:
- Batch processing progress
- Success/failure counts per batch
- Overall statistics
- Error details for failed emails

Check server logs for detailed information about bulk email operations.
