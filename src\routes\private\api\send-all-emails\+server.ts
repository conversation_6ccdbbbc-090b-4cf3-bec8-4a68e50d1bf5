import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { sendEmailToAllGuests } from '$lib/server/sendEmail';

export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
    try {
        const { guestIds, eventDetails, batchSize = 50 } = await request.json();

        // Validate input
        if (!guestIds || !Array.isArray(guestIds) || !eventDetails) {
            return json({ success: false, error: 'Missing guest IDs array or event details' }, { status: 400 });
        }

        if (guestIds.length === 0) {
            return json({ success: false, error: 'No guest IDs provided' }, { status: 400 });
        }

        console.log(`Starting bulk email send to ${guestIds.length} guests`);

        // Fetch guest data from database in batches
        const allResults = {
            total: guestIds.length,
            successful: 0,
            failed: 0,
            results: [] as Array<{
                guestEmail: string | null;
                guestName: string;
                success: boolean;
                error?: any;
            }>
        };

        // Process guests in batches to avoid memory issues
        for (let i = 0; i < guestIds.length; i += batchSize) {
            const batchIds = guestIds.slice(i, i + batchSize);

            // Fetch guest data for this batch
            const { data: guests, error: fetchError } = await supabase
                .from('workforce')
                .select('*')
                .in('id', batchIds.map(id => typeof id === 'string' ? parseInt(id) : id));

            if (fetchError) {
                console.error('Error fetching guest batch:', fetchError);
                // Mark all guests in this batch as failed
                for (const id of batchIds) {
                    allResults.failed++;
                    allResults.results.push({
                        guestEmail: null,
                        guestName: `Guest ID: ${id}`,
                        success: false,
                        error: 'Failed to fetch guest data'
                    });
                }
                continue;
            }

            if (!guests || guests.length === 0) {
                console.warn('No guests found for batch:', batchIds);
                continue;
            }

            // Send emails for this batch
            const batchResult = await sendEmailToAllGuests(guests, eventDetails);

            // Merge batch results into overall results
            allResults.successful += batchResult.successful;
            allResults.failed += batchResult.failed;
            allResults.results.push(...batchResult.results);

            // Update email status in database for successful sends in this batch
            if (batchResult.successful > 0) {
                try {
                    const successfulEmails = batchResult.results
                        .filter(r => r.success)
                        .map(r => r.guestEmail);

                    if (successfulEmails.length > 0) {
                        // Find guest IDs for successful emails
                        const successfulGuestIds = guests
                            .filter(guest => successfulEmails.includes(guest.email))
                            .map(guest => guest.id);

                        const { error: updateError } = await supabase
                            .from('workforce')
                            .update({
                                email_status: 'sent',
                                email_sent_at: new Date().toISOString()
                            })
                            .in('id', successfulGuestIds);

                        if (updateError) {
                            console.error('Error updating email statuses:', updateError);
                        } else {
                            console.log(`Updated email status for ${successfulGuestIds.length} guests in batch`);
                        }
                    }
                } catch (dbError) {
                    console.error('Database update error:', dbError);
                }
            }

            // Update failed email status for this batch
            if (batchResult.failed > 0) {
                try {
                    const failedEmails = batchResult.results
                        .filter(r => !r.success)
                        .map(r => r.guestEmail);

                    if (failedEmails.length > 0) {
                        // Find guest IDs for failed emails
                        const failedGuestIds = guests
                            .filter(guest => failedEmails.includes(guest.email))
                            .map(guest => guest.id);

                        const { error: updateError } = await supabase
                            .from('workforce')
                            .update({
                                email_status: 'failed',
                                email_sent_at: new Date().toISOString()
                            })
                            .in('id', failedGuestIds);

                        if (updateError) {
                            console.error('Error updating failed email statuses:', updateError);
                        } else {
                            console.log(`Updated failed email status for ${failedGuestIds.length} guests in batch`);
                        }
                    }
                } catch (dbError) {
                    console.error('Database update error for failed emails:', dbError);
                }
            }

            console.log(`Completed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(guestIds.length / batchSize)}`);
        }

        console.log('All batches completed:');
        console.log('- Total guests:', allResults.total);
        console.log('- Successful:', allResults.successful);
        console.log('- Failed:', allResults.failed);

        return json({
            success: true,
            message: `Email sending completed. ${allResults.successful} successful, ${allResults.failed} failed.`,
            data: allResults
        });

    } catch (error) {
        console.error('Error in send-all-emails API:', error);
        return json({
            success: false,
            error: 'Internal server error'
        }, { status: 500 });
    }
};
