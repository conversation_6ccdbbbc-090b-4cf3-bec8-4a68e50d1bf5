/**
 * Utility for sending emails in batches to avoid payload size issues
 */

export interface BatchEmailResult {
    total: number;
    successful: number;
    failed: number;
    results: Array<{
        guestEmail: string | null;
        guestName: string;
        success: boolean;
        error?: any;
    }>;
}

export interface BatchEmailProgress {
    currentBatch: number;
    totalBatches: number;
    overallProgress: {
        total: number;
        successful: number;
        failed: number;
    };
    isComplete: boolean;
}

/**
 * Send emails to guests in batches with progress tracking
 */
export async function sendEmailsInBatches(
    guestIds: string[],
    eventDetails: any,
    batchSize: number = 25,
    onProgress?: (progress: BatchEmailProgress) => void
): Promise<BatchEmailResult> {
    const totalGuests = guestIds.length;
    const totalBatches = Math.ceil(totalGuests / batchSize);
    
    const overallResult: BatchEmailResult = {
        total: totalGuests,
        successful: 0,
        failed: 0,
        results: []
    };

    console.log(`Starting batch email send: ${totalGuests} guests in ${totalBatches} batches`);

    for (let i = 0; i < totalBatches; i++) {
        const startIndex = i * batchSize;
        const endIndex = Math.min(startIndex + batchSize, totalGuests);
        const batchGuestIds = guestIds.slice(startIndex, endIndex);

        console.log(`Processing batch ${i + 1}/${totalBatches} (${batchGuestIds.length} guests)`);

        try {
            const response = await fetch('/private/api/send-emails-batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    guestIds: batchGuestIds,
                    eventDetails,
                    batchIndex: i,
                    totalBatches
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const batchResult = await response.json();

            if (batchResult.success) {
                // Merge batch results into overall results
                overallResult.successful += batchResult.data.successful;
                overallResult.failed += batchResult.data.failed;
                overallResult.results.push(...batchResult.data.results);

                // Report progress
                if (onProgress) {
                    onProgress({
                        currentBatch: i + 1,
                        totalBatches,
                        overallProgress: {
                            total: overallResult.total,
                            successful: overallResult.successful,
                            failed: overallResult.failed
                        },
                        isComplete: i === totalBatches - 1
                    });
                }

                console.log(`Batch ${i + 1}/${totalBatches} completed successfully`);
            } else {
                console.error(`Batch ${i + 1}/${totalBatches} failed:`, batchResult.error);
                
                // Mark all guests in this batch as failed
                for (let j = 0; j < batchGuestIds.length; j++) {
                    overallResult.failed++;
                    overallResult.results.push({
                        guestEmail: null,
                        guestName: `Guest ID: ${batchGuestIds[j]}`,
                        success: false,
                        error: batchResult.error || 'Batch processing failed'
                    });
                }
            }
        } catch (error) {
            console.error(`Error processing batch ${i + 1}/${totalBatches}:`, error);
            
            // Mark all guests in this batch as failed
            for (let j = 0; j < batchGuestIds.length; j++) {
                overallResult.failed++;
                overallResult.results.push({
                    guestEmail: null,
                    guestName: `Guest ID: ${batchGuestIds[j]}`,
                    success: false,
                    error: error instanceof Error ? error.message : 'Network error'
                });
            }
        }

        // Add a small delay between batches to avoid overwhelming the server
        if (i < totalBatches - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    console.log('All batches completed:');
    console.log(`- Total: ${overallResult.total}`);
    console.log(`- Successful: ${overallResult.successful}`);
    console.log(`- Failed: ${overallResult.failed}`);

    return overallResult;
}

/**
 * Split an array into chunks of specified size
 */
export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
        chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
}
