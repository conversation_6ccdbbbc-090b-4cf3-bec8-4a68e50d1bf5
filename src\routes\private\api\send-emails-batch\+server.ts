import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { sendEmailToGuest } from '$lib/server/sendEmail';

/**
 * Alternative batch email endpoint that processes smaller chunks
 * This endpoint processes a single batch of guests to avoid payload size issues
 */
export const POST: RequestHandler = async ({ request, locals: { supabase } }) => {
    try {
        const { guestIds, eventDetails, batchIndex = 0, totalBatches = 1 } = await request.json();

        // Validate input
        if (!guestIds || !Array.isArray(guestIds) || !eventDetails) {
            return json({ success: false, error: 'Missing guest IDs array or event details' }, { status: 400 });
        }

        if (guestIds.length === 0) {
            return json({ success: false, error: 'No guest IDs provided' }, { status: 400 });
        }

        console.log(`Processing batch ${batchIndex + 1}/${totalBatches} with ${guestIds.length} guests`);

        // Fetch guest data from database
        const { data: guests, error: fetchError } = await supabase
            .from('workforce')
            .select('*')
            .in('id', guestIds.map(id => parseInt(id)));

        if (fetchError) {
            console.error('Error fetching guests:', fetchError);
            return json({
                success: false,
                error: 'Failed to fetch guest data',
                batchIndex,
                totalBatches
            }, { status: 500 });
        }

        if (!guests || guests.length === 0) {
            return json({
                success: true,
                message: 'No guests found for this batch',
                data: {
                    total: 0,
                    successful: 0,
                    failed: 0,
                    results: []
                },
                batchIndex,
                totalBatches
            });
        }

        // Filter guests with valid email addresses
        const validGuests = guests.filter(guest =>
            guest.email &&
            guest.first_name &&
            guest.qr_data
        );

        console.log(`Valid guests in batch: ${validGuests.length}/${guests.length}`);

        const results = {
            total: guests.length,
            successful: 0,
            failed: 0,
            results: [] as Array<{
                guestEmail: string | null;
                guestName: string;
                success: boolean;
                error?: any;
            }>
        };

        // Send emails to each valid guest
        for (const guest of validGuests) {
            try {
                const result = await sendEmailToGuest(guest, eventDetails);

                if (result.success) {
                    results.successful++;
                    results.results.push({
                        guestEmail: guest.email,
                        guestName: guest.last_name ? `${guest.first_name} ${guest.last_name}` : guest.first_name || 'Unknown',
                        success: true
                    });

                    // Update email status in database
                    try {
                        const { error: updateError } = await supabase
                            .from('workforce')
                            .update({
                                email_status: 'sent',
                                email_sent_at: new Date().toISOString()
                            })
                            .eq('id', guest.id);

                        if (updateError) {
                            console.error('Error updating email status:', updateError);
                        }
                    } catch (dbError) {
                        console.error('Database update error:', dbError);
                    }
                } else {
                    results.failed++;
                    results.results.push({
                        guestEmail: guest.email,
                        guestName: guest.last_name ? `${guest.first_name} ${guest.last_name}` : guest.first_name || 'Unknown',
                        success: false,
                        error: result.error
                    });

                    // Update failed email status
                    try {
                        const { error: updateError } = await supabase
                            .from('workforce')
                            .update({
                                email_status: 'failed',
                                email_sent_at: new Date().toISOString()
                            })
                            .eq('id', guest.id);

                        if (updateError) {
                            console.error('Error updating failed email status:', updateError);
                        }
                    } catch (dbError) {
                        console.error('Database update error for failed email:', dbError);
                    }
                }
            } catch (error) {
                results.failed++;
                results.results.push({
                    guestEmail: guest.email,
                    guestName: guest.last_name ? `${guest.first_name} ${guest.last_name}` : guest.first_name || 'Unknown',
                    success: false,
                    error: error
                });
                console.error('Unexpected error sending email to guest:', guest.email, error);
            }

            // Add a small delay between emails to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Handle invalid guests (missing email/first_name/qr_data)
        const invalidGuests = guests.filter(guest =>
            !guest.email || !guest.first_name || !guest.qr_data
        );

        for (const guest of invalidGuests) {
            results.failed++;
            results.results.push({
                guestEmail: guest.email,
                guestName: guest.last_name ? `${guest.first_name} ${guest.last_name}` : guest.first_name || 'Unknown',
                success: false,
                error: 'Missing required fields (email, first_name, or qr_data)'
            });
        }

        console.log(`Batch ${batchIndex + 1}/${totalBatches} completed:`);
        console.log(`- Total: ${results.total}, Successful: ${results.successful}, Failed: ${results.failed}`);

        return json({
            success: true,
            message: `Batch ${batchIndex + 1}/${totalBatches} completed. ${results.successful} successful, ${results.failed} failed.`,
            data: results,
            batchIndex,
            totalBatches,
            isLastBatch: batchIndex === totalBatches - 1
        });

    } catch (error) {
        console.error('Error in send-emails-batch API:', error);
        return json({
            success: false,
            error: 'Internal server error'
        }, { status: 500 });
    }
};
